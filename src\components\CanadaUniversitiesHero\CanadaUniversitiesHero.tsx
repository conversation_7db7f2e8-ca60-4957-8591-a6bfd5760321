'use client';

import { useState, useEffect } from 'react';
import styles from './CanadaUniversitiesHero.module.css';

export default function CanadaUniversitiesHero() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const stats = [
    {
      number: '50+',
      label: 'Partner Universities',
      icon: 'fa-solid fa-university'
    },
    {
      number: '200+',
      label: 'Programs Available',
      icon: 'fa-solid fa-graduation-cap'
    },
    {
      number: '15,000+',
      label: 'International Students',
      icon: 'fa-solid fa-users'
    },
    {
      number: '95%',
      label: 'Success Rate',
      icon: 'fa-solid fa-trophy'
    }
  ];

  const highlights = [
    {
      icon: 'fa-solid fa-maple-leaf',
      text: 'World-Class Education System'
    },
    {
      icon: 'fa-solid fa-heart',
      text: 'High Quality of Life'
    },
    {
      icon: 'fa-solid fa-passport',
      text: 'Post-Study Work Permits'
    },
    {
      icon: 'fa-solid fa-globe-americas',
      text: 'Pathway to Permanent Residency'
    }
  ];

  return (
    <section className={styles.heroSection}>
      {/* Background Elements */}
      <div className={styles.backgroundElements}>
        <div className={styles.bgShape1}></div>
        <div className={styles.bgShape2}></div>
        <div className={styles.bgShape3}></div>
      </div>

      <div className={styles.container}>
        <div className={styles.heroContent}>
          {/* Left Content */}
          <div className={styles.leftContent}>
            <div className={`${styles.heroText} ${isVisible ? styles.fadeInUp : ''}`}>
              <div className={styles.breadcrumb}>
                <span>Home</span>
                <i className="fa-solid fa-chevron-right"></i>
                <span>Universities</span>
                <i className="fa-solid fa-chevron-right"></i>
                <span className={styles.currentPage}>Canada</span>
              </div>
              
              <h1 className={styles.heroTitle}>
                Study in <span className={styles.highlight}>Canada</span>
                <br />
                Your Gateway to Excellence
              </h1>
              
              <p className={styles.heroDescription}>
                Discover world-renowned Canadian universities offering exceptional education, 
                multicultural experiences, and pathways to permanent residency. From coast to coast, 
                Canada provides unparalleled opportunities for international students seeking 
                academic excellence and a bright future.
              </p>

              <div className={styles.highlightsGrid}>
                {highlights.map((highlight, index) => (
                  <div 
                    key={index}
                    className={`${styles.highlightItem} ${isVisible ? styles.slideInLeft : ''}`}
                    style={{'--delay': `${index * 0.1}s`} as React.CSSProperties}
                  >
                    <div className={styles.highlightIcon}>
                      <i className={highlight.icon}></i>
                    </div>
                    <span>{highlight.text}</span>
                  </div>
                ))}
              </div>

              <div className={styles.ctaButtons}>
                <button className={styles.primaryBtn}>
                  <span>Explore Universities</span>
                  <i className="fa-solid fa-arrow-right"></i>
                </button>
                <button className={styles.secondaryBtn}>
                  <i className="fa-solid fa-play"></i>
                  <span>Watch Video</span>
                </button>
              </div>
            </div>
          </div>

          {/* Right Content - Stats & Flag */}
          <div className={styles.rightContent}>
            <div className={`${styles.flagContainer} ${isVisible ? styles.slideInRight : ''}`}>
              <img 
                src="https://asvbqmdvplqupbqpigoa.supabase.co/storage/v1/object/public/learneducation/flags/canada-flag.svg" 
                alt="Canada Flag" 
                className={styles.flagImage}
              />
              <div className={styles.flagOverlay}></div>
            </div>

            <div className={styles.statsGrid}>
              {stats.map((stat, index) => (
                <div 
                  key={index}
                  className={`${styles.statCard} ${isVisible ? styles.fadeInUp : ''}`}
                  style={{'--delay': `${(index + 1) * 0.15}s`} as React.CSSProperties}
                >
                  <div className={styles.statIcon}>
                    <i className={stat.icon}></i>
                  </div>
                  <div className={styles.statContent}>
                    <div className={styles.statNumber}>{stat.number}</div>
                    <div className={styles.statLabel}>{stat.label}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Info Bar */}
        <div className={`${styles.infoBar} ${isVisible ? styles.slideInUp : ''}`}>
          <div className={styles.infoItem}>
            <i className="fa-solid fa-clock"></i>
            <div>
              <span className={styles.infoLabel}>Application Deadline</span>
              <span className={styles.infoValue}>Rolling Admissions</span>
            </div>
          </div>
          <div className={styles.infoItem}>
            <i className="fa-solid fa-calendar-alt"></i>
            <div>
              <span className={styles.infoLabel}>Intake Seasons</span>
              <span className={styles.infoValue}>Fall, Winter, Summer</span>
            </div>
          </div>
          <div className={styles.infoItem}>
            <i className="fa-solid fa-dollar-sign"></i>
            <div>
              <span className={styles.infoLabel}>Tuition Range</span>
              <span className={styles.infoValue}>CAD $15,000 - $35,000</span>
            </div>
          </div>
          <div className={styles.infoItem}>
            <i className="fa-solid fa-language"></i>
            <div>
              <span className={styles.infoLabel}>Language Requirements</span>
              <span className={styles.infoValue}>IELTS 6.0+ / TOEFL 80+</span>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className={styles.floatingElements}>
        <div className={styles.floatingIcon1}>
          <i className="fa-solid fa-maple-leaf"></i>
        </div>
        <div className={styles.floatingIcon2}>
          <i className="fa-solid fa-graduation-cap"></i>
        </div>
        <div className={styles.floatingIcon3}>
          <i className="fa-solid fa-university"></i>
        </div>
      </div>
    </section>
  );
}
