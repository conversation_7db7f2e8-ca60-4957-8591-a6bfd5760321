/* Canada Universities Hero Section */
.heroSection {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 25%, #1e293b 50%, #0f172a 75%, #1e293b 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 120px 0 80px;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float 15s ease-in-out infinite;
}

.bgShape1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(45deg, #dc2626, #ffffff);
  top: -20%;
  left: -15%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(45deg, #ffffff, #dc2626);
  bottom: -15%;
  right: -10%;
  animation-delay: 5s;
}

.bgShape3 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #dc2626, #ffffff);
  top: 50%;
  right: 20%;
  animation-delay: 10s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-20px) rotate(90deg); }
  50% { transform: translateY(-40px) rotate(180deg); }
  75% { transform: translateY(-20px) rotate(270deg); }
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  width: 100%;
}

/* Hero Content */
.heroContent {
  display: grid;
  grid-template-columns: 1fr 500px;
  gap: 80px;
  align-items: center;
  margin-bottom: 60px;
}

/* Left Content */
.leftContent {
  opacity: 1;
  transform: translateY(0);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #94a3b8;
  margin-bottom: 30px;
}

.breadcrumb i {
  font-size: 0.7rem;
  color: #64748b;
}

.currentPage {
  color: #dc2626 !important;
  font-weight: 600;
}

.heroTitle {
  font-size: 4rem;
  font-weight: 900;
  color: #f1f5f9;
  line-height: 1.1;
  margin-bottom: 30px;
  letter-spacing: -0.02em;
}

.highlight {
  background: linear-gradient(135deg, #dc2626, #ffffff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.heroDescription {
  font-size: 1.2rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin-bottom: 40px;
  max-width: 600px;
}

/* Highlights Grid */
.highlightsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 50px;
}

.highlightItem {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(220, 38, 38, 0.2);
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateX(0);
}

.highlightItem:hover {
  background: rgba(220, 38, 38, 0.1);
  border-color: rgba(220, 38, 38, 0.4);
  transform: translateX(8px);
}

.highlightIcon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.highlightItem span {
  color: #e2e8f0;
  font-weight: 500;
  font-size: 0.95rem;
}

/* CTA Buttons */
.ctaButtons {
  display: flex;
  gap: 20px;
  align-items: center;
}

.primaryBtn {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 18px 35px;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 10px 30px rgba(220, 38, 38, 0.3);
}

.primaryBtn:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(220, 38, 38, 0.4);
}

.primaryBtn i {
  transition: transform 0.3s ease;
}

.primaryBtn:hover i {
  transform: translateX(5px);
}

.secondaryBtn {
  background: transparent;
  color: #e2e8f0;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 16px 30px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
}

.secondaryBtn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(220, 38, 38, 0.5);
  color: #dc2626;
}

/* Right Content */
.rightContent {
  display: flex;
  flex-direction: column;
  gap: 40px;
  opacity: 1;
  transform: translateX(0);
}

/* Flag Container */
.flagContainer {
  position: relative;
  width: 100%;
  height: 300px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
}

.flagImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.flagContainer:hover .flagImage {
  transform: scale(1.05);
}

.flagOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(220, 38, 38, 0.2) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.statCard {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  opacity: 1;
  transform: translateY(0);
}

.statCard:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(220, 38, 38, 0.3);
  transform: translateY(-5px);
}

.statIcon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.statContent {
  flex: 1;
}

.statNumber {
  font-size: 1.8rem;
  font-weight: 900;
  color: #f1f5f9;
  line-height: 1;
  margin-bottom: 5px;
}

.statLabel {
  font-size: 0.9rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Info Bar */
.infoBar {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  opacity: 1;
  transform: translateY(0);
}

.infoItem {
  display: flex;
  align-items: center;
  gap: 15px;
}

.infoItem i {
  color: #dc2626;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.infoLabel {
  display: block;
  font-size: 0.85rem;
  color: #94a3b8;
  font-weight: 500;
  margin-bottom: 2px;
}

.infoValue {
  display: block;
  font-size: 0.95rem;
  color: #e2e8f0;
  font-weight: 600;
}

/* Floating Elements */
.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floatingIcon1,
.floatingIcon2,
.floatingIcon3 {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(220, 38, 38, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 1.5rem;
  animation: floatIcon 8s ease-in-out infinite;
}

.floatingIcon1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floatingIcon2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floatingIcon3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes floatIcon {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Animation Classes */
.fadeInUp {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

.slideInLeft {
  opacity: 1;
  transform: translateX(0);
  animation: slideInLeft 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

.slideInRight {
  opacity: 1;
  transform: translateX(0);
  animation: slideInRight 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

.slideInUp {
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.8s ease-out both;
  animation-delay: var(--delay, 0s);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .heroContent {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }
  
  .rightContent {
    order: -1;
  }
  
  .flagContainer {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .heroSection {
    padding: 100px 0 60px;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .heroTitle {
    font-size: 2.8rem;
  }
  
  .highlightsGrid {
    grid-template-columns: 1fr;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .infoBar {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2.2rem;
  }
  
  .heroDescription {
    font-size: 1.1rem;
  }
  
  .primaryBtn,
  .secondaryBtn {
    width: 100%;
    justify-content: center;
  }
}
