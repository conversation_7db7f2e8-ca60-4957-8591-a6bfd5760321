/* Featured Ireland Universities Section */
.featuredSection {
  padding: 100px 0;
  background: linear-gradient(135deg, 
    #0f172a 0%, 
    #1e293b 25%, 
    #0f172a 50%, 
    #1e293b 75%, 
    #0f172a 100%
  );
  position: relative;
  overflow: hidden;
}

.featuredSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(249, 115, 22, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.sectionHeader.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #22c55e, #f97316);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20px;
  line-height: 1.2;
}

.sectionDescription {
  font-size: 1.25rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

/* University Card */
.universityCard {
  position: relative;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
  border-radius: 24px;
  overflow: hidden;
  border: 2px solid transparent;
  background-clip: padding-box;
  backdrop-filter: blur(20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateY(50px) scale(0.95);
}

.universityCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  padding: 2px;
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.3) 0%,
    rgba(249, 115, 22, 0.3) 50%,
    rgba(59, 130, 246, 0.3) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.universityCard.slideInUp {
  opacity: 1;
  transform: translateY(0) scale(1);
  animation-delay: var(--delay);
  animation-duration: 0.8s;
  animation-fill-mode: both;
}

.universityCard:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.25),
    0 15px 30px rgba(34, 197, 94, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.universityCard:hover::before {
  opacity: 1;
}

/* University Badge */
.universityBadge {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  z-index: 10;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  transform: scale(0.95);
}

.universityCard:hover .universityBadge {
  transform: scale(1);
  box-shadow:
    0 12px 30px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Badge Colors */
.elite {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  color: #1f2937;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.research {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.innovation {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #c2410c 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.popular {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.topRated {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.friendly {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* University Image */
.universityImage {
  position: relative;
  height: 280px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
}

.universityImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.1) 0%,
    transparent 30%,
    transparent 70%,
    rgba(249, 115, 22, 0.1) 100%
  );
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.universityImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(0.9) contrast(1.1);
}

.universityCard:hover .universityImage::before {
  opacity: 1;
}

.universityCard:hover .universityImage img {
  transform: scale(1.08);
  filter: brightness(1) contrast(1.2);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 1;
}

/* University Content */
.universityContent {
  padding: 2.5rem;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.01) 100%
  );
}

.universityName {
  font-size: 1.6rem;
  font-weight: 800;
  background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1.25rem;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

.locationInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #94a3b8;
  margin-bottom: 2rem;
  font-weight: 600;
  font-size: 0.95rem;
  padding: 0.75rem 1rem;
  background: rgba(34, 197, 94, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.locationInfo i {
  color: #22c55e;
  font-size: 1.1rem;
}

/* University Stats */
.universityStats {
  display: grid;
  gap: 1rem;
  margin-bottom: 2.5rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: #e2e8f0;
  padding: 1rem 1.25rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  font-weight: 500;
}

.statItem:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-color: rgba(34, 197, 94, 0.3);
  transform: translateX(5px);
}

.statItem i {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border-radius: 8px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

/* Learn More Button */
.learnMoreBtn {
  width: 100%;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
  color: white;
  border: none;
  padding: 1.25rem 2rem;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow:
    0 8px 20px rgba(34, 197, 94, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.learnMoreBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.learnMoreBtn:hover::before {
  left: 100%;
}

.learnMoreBtn:hover {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 50%, #14532d 100%);
  transform: translateY(-3px);
  box-shadow:
    0 15px 35px rgba(34, 197, 94, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.learnMoreBtn i {
  transition: transform 0.3s ease;
  font-size: 1.1rem;
}

.learnMoreBtn:hover i {
  transform: translateX(8px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .universitiesGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .featuredSection {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .sectionDescription {
    font-size: 1.1rem;
  }
  
  .universitiesGrid {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .universityCard {
    margin: 0 auto;
    max-width: 400px;
  }
  
  .universityContent {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .sectionTitle {
    font-size: 2rem;
  }
  
  .universityImage {
    height: 200px;
  }
  
  .universityContent {
    padding: 1rem;
  }
}
