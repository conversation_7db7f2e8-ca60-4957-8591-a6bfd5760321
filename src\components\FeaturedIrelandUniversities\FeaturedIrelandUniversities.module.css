/* Featured Ireland Universities Section */
.featuredSection {
  padding: 100px 0;
  background: linear-gradient(135deg, 
    #0f172a 0%, 
    #1e293b 25%, 
    #0f172a 50%, 
    #1e293b 75%, 
    #0f172a 100%
  );
  position: relative;
  overflow: hidden;
}

.featuredSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(249, 115, 22, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.sectionHeader.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #22c55e, #f97316);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20px;
  line-height: 1.2;
}

.sectionDescription {
  font-size: 1.25rem;
  color: #cbd5e1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Universities Grid */
.universitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

/* University Card */
.universityCard {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateY(50px);
}

.universityCard.slideInUp {
  opacity: 1;
  transform: translateY(0);
  animation-delay: var(--delay);
  animation-duration: 0.8s;
  animation-fill-mode: both;
}

.universityCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

/* University Badge */
.universityBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Badge Colors */
.elite {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #1f2937;
}

.research {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.innovation {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
}

.popular {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
}

.topRated {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.friendly {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

/* University Image */
.universityImage {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.universityImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.universityCard:hover .universityImage img {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* University Content */
.universityContent {
  padding: 2rem;
}

.universityName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.locationInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.locationInfo i {
  color: #22c55e;
}

/* University Stats */
.universityStats {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #cbd5e1;
  padding: 0.5rem 0;
}

.statItem i {
  width: 20px;
  text-align: center;
  color: #22c55e;
  font-size: 1rem;
}

/* Learn More Button */
.learnMoreBtn {
  width: 100%;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.learnMoreBtn:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(34, 197, 94, 0.3);
}

.learnMoreBtn i {
  transition: transform 0.3s ease;
}

.learnMoreBtn:hover i {
  transform: translateX(5px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .universitiesGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .featuredSection {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .sectionDescription {
    font-size: 1.1rem;
  }
  
  .universitiesGrid {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .universityCard {
    margin: 0 auto;
    max-width: 400px;
  }
  
  .universityContent {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .sectionTitle {
    font-size: 2rem;
  }
  
  .universityImage {
    height: 200px;
  }
  
  .universityContent {
    padding: 1rem;
  }
}
